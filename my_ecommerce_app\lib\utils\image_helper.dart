import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ImageHelper {
  // صور placeholder للمنتجات
  static const Map<String, String> placeholderImages = {
    'sunglasses':
        'https://via.placeholder.com/300x300/4A90E2/FFFFFF?text=نظارة+شمسية',
    'reading':
        'https://via.placeholder.com/300x300/50C878/FFFFFF?text=نظارة+قراءة',
    'fashion':
        'https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=نظارة+أزياء',
    'sports':
        'https://via.placeholder.com/300x300/FFA500/FFFFFF?text=نظارة+رياضية',
    'computer':
        'https://via.placeholder.com/300x300/9370DB/FFFFFF?text=نظارة+كمبيوتر',
    'safety':
        'https://via.placeholder.com/300x300/32CD32/FFFFFF?text=نظارة+أمان',
    'default':
        'https://via.placeholder.com/300x300/CCCCCC/666666?text=صورة+غير+متاحة',
  };

  // الحصول على صورة placeholder حسب نوع المنتج
  static String getPlaceholderImage(String productType) {
    return placeholderImages[productType.toLowerCase()] ??
        placeholderImages['default']!;
  }

  // بناء widget للصورة مع معالجة الأخطاء
  static Widget buildProductImage({
    required String imageUrl,
    required double width,
    required double height,
    String fallbackType = 'default',
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    // إذا كانت الصورة placeholder محلي، اعرض placeholder مباشرة
    if (imageUrl.startsWith('placeholder_')) {
      String type = imageUrl.replaceFirst('placeholder_', '');
      return buildColoredPlaceholder(
        width: width,
        height: height,
        text: _getPlaceholderText(type),
        backgroundColor: _getPlaceholderColor(type),
        borderRadius: borderRadius,
      );
    }

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => Container(
          width: width,
          height: height,
          color: Colors.grey[200],
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.teal.shade300),
            ),
          ),
        ),
        errorWidget: (context, url, error) {
          // في حالة الخطأ، استخدم صورة placeholder
          return buildColoredPlaceholder(
            width: width,
            height: height,
            text: _getPlaceholderText(fallbackType),
            backgroundColor: _getPlaceholderColor(fallbackType),
            borderRadius: borderRadius,
          );
        },
      ),
    );
  }

  // الحصول على نص placeholder
  static String _getPlaceholderText(String type) {
    switch (type.toLowerCase()) {
      case 'sunglasses':
        return 'نظارة شمسية';
      case 'reading':
        return 'نظارة قراءة';
      case 'fashion':
        return 'نظارة أزياء';
      case 'sports':
        return 'نظارة رياضية';
      case 'prescription':
        return 'نظارة طبية';
      default:
        return 'نظارة';
    }
  }

  // الحصول على لون placeholder
  static Color _getPlaceholderColor(String type) {
    switch (type.toLowerCase()) {
      case 'sunglasses':
        return const Color(0xFF4A90E2);
      case 'reading':
        return const Color(0xFF50C878);
      case 'fashion':
        return const Color(0xFFFF6B6B);
      case 'sports':
        return const Color(0xFFFFA500);
      case 'prescription':
        return const Color(0xFF9370DB);
      default:
        return const Color(0xFFCCCCCC);
    }
  }

  // إنشاء صورة placeholder ملونة
  static Widget buildColoredPlaceholder({
    required double width,
    required double height,
    required String text,
    Color backgroundColor = Colors.grey,
    Color textColor = Colors.white,
    BorderRadius? borderRadius,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: width * 0.08,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
