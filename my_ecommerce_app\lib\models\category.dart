enum CategoryType {
  glasses, // النظارات
  accessories, // الإكسسوارات
  lenses, // العدسات
  services, // الخدمات
  content // المحتوى التوعوي
}

enum GlassesSubType {
  prescription, // نظارات طبية
  sunglasses, // نظارات شمسية
  reading, // نظارات قراءة
  computer, // نظارات كمبيوتر
  customPrescription // نظارات مخصصة بالوصفة
}

enum AccessoryType {
  cases, // علب النظارات
  cleaningLenses, // عدسات تنظيف
  cleaningCloths, // محارم خاصة بالتنظيف
  chains, // سلاسل / أحبال للنظارات
  lensProtectors, // واقي عدسات
  bluelightFilters // واقي ضد الأشعة الزرقاء
}

enum LensType {
  prescription, // عدسات طبية
  contact, // عدسات لاصقة
  solutions // حلول العدسات
}

class Category {
  final String id;
  final String name;
  final String nameEn;
  final String description;
  final String icon;
  final String image;
  final CategoryType type;
  final List<SubCategory> subCategories;
  final bool isActive;
  final int sortOrder;
  final String? parentId;

  Category({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.description,
    required this.icon,
    required this.image,
    required this.type,
    this.subCategories = const [],
    this.isActive = true,
    this.sortOrder = 0,
    this.parentId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'nameEn': nameEn,
      'description': description,
      'icon': icon,
      'image': image,
      'type': type.toString(),
      'subCategories': subCategories.map((sub) => sub.toJson()).toList(),
      'isActive': isActive,
      'sortOrder': sortOrder,
      'parentId': parentId,
    };
  }

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'],
      name: json['name'],
      nameEn: json['nameEn'],
      description: json['description'],
      icon: json['icon'],
      image: json['image'],
      type: CategoryType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => CategoryType.glasses,
      ),
      subCategories: (json['subCategories'] as List<dynamic>?)
              ?.map((sub) => SubCategory.fromJson(sub))
              .toList() ??
          [],
      isActive: json['isActive'] ?? true,
      sortOrder: json['sortOrder'] ?? 0,
      parentId: json['parentId'],
    );
  }

  // البيانات التجريبية للفئات
  static List<Category> get demoCategories => _demoCategories;
}

class SubCategory {
  final String id;
  final String name;
  final String nameEn;
  final String description;
  final String icon;
  final String? image;
  final bool isActive;
  final int sortOrder;
  final String? targetGender; // men, women, kids, unisex

  SubCategory({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.description,
    required this.icon,
    this.image,
    this.isActive = true,
    this.sortOrder = 0,
    this.targetGender,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'nameEn': nameEn,
      'description': description,
      'icon': icon,
      'image': image,
      'isActive': isActive,
      'sortOrder': sortOrder,
      'targetGender': targetGender,
    };
  }

  factory SubCategory.fromJson(Map<String, dynamic> json) {
    return SubCategory(
      id: json['id'],
      name: json['name'],
      nameEn: json['nameEn'],
      description: json['description'],
      icon: json['icon'],
      image: json['image'],
      isActive: json['isActive'] ?? true,
      sortOrder: json['sortOrder'] ?? 0,
      targetGender: json['targetGender'],
    );
  }
}

// البيانات التجريبية
final List<Category> _demoCategories = [
  // 1. النظارات
  Category(
    id: 'glasses',
    name: 'النظارات',
    nameEn: 'Glasses',
    description: 'جميع أنواع النظارات الطبية والشمسية',
    icon: '👓',
    image: 'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=400',
    type: CategoryType.glasses,
    subCategories: [
      // نظارات طبية
      SubCategory(
        id: 'prescription',
        name: 'نظارات طبية',
        nameEn: 'Prescription Glasses',
        description: 'نظارات طبية بجودة عالية',
        icon: '👓',
        image:
            'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=300',
      ),
      SubCategory(
        id: 'prescription_men',
        name: 'للرجال',
        nameEn: 'Men\'s Prescription',
        description: 'نظارات طبية رجالية',
        icon: '👨‍💼',
        targetGender: 'men',
      ),
      SubCategory(
        id: 'prescription_women',
        name: 'للنساء',
        nameEn: 'Women\'s Prescription',
        description: 'نظارات طبية نسائية',
        icon: '👩‍💼',
        targetGender: 'women',
      ),
      SubCategory(
        id: 'prescription_kids',
        name: 'للأطفال',
        nameEn: 'Kids Prescription',
        description: 'نظارات طبية للأطفال',
        icon: '👶',
        targetGender: 'kids',
      ),
      SubCategory(
        id: 'frames_only',
        name: 'إطارات فقط',
        nameEn: 'Frames Only',
        description: 'إطارات بدون عدسات',
        icon: '🔲',
      ),

      // نظارات شمسية
      SubCategory(
        id: 'sunglasses',
        name: 'نظارات شمسية',
        nameEn: 'Sunglasses',
        description: 'نظارات شمسية عصرية',
        icon: '🕶️',
        image:
            'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=300',
      ),
      SubCategory(
        id: 'sunglasses_men',
        name: 'رجالية',
        nameEn: 'Men\'s Sunglasses',
        description: 'نظارات شمسية رجالية',
        icon: '🕶️',
        targetGender: 'men',
      ),
      SubCategory(
        id: 'sunglasses_women',
        name: 'نسائية',
        nameEn: 'Women\'s Sunglasses',
        description: 'نظارات شمسية نسائية',
        icon: '🕶️',
        targetGender: 'women',
      ),
      SubCategory(
        id: 'sunglasses_kids',
        name: 'للأطفال',
        nameEn: 'Kids Sunglasses',
        description: 'نظارات شمسية للأطفال',
        icon: '🕶️',
        targetGender: 'kids',
      ),
      SubCategory(
        id: 'sports_sunglasses',
        name: 'رياضية',
        nameEn: 'Sports Sunglasses',
        description: 'نظارات شمسية رياضية',
        icon: '🏃‍♂️',
      ),
      SubCategory(
        id: 'polarized',
        name: 'مضادة للوهج',
        nameEn: 'Polarized',
        description: 'نظارات مضادة للوهج',
        icon: '✨',
      ),

      // أنواع أخرى
      SubCategory(
        id: 'reading',
        name: 'نظارات قراءة',
        nameEn: 'Reading Glasses',
        description: 'نظارات قراءة مريحة',
        icon: '📖',
      ),
      SubCategory(
        id: 'computer',
        name: 'نظارات كمبيوتر',
        nameEn: 'Computer Glasses',
        description: 'حماية من الشاشات',
        icon: '💻',
      ),
      SubCategory(
        id: 'custom_prescription',
        name: 'مخصصة بالوصفة',
        nameEn: 'Custom Prescription',
        description: 'نظارات مخصصة حسب الوصفة',
        icon: '📋',
      ),
    ],
  ),

  // 2. الإكسسوارات
  Category(
    id: 'accessories',
    name: 'الإكسسوارات',
    nameEn: 'Accessories',
    description: 'إكسسوارات النظارات والعناية',
    icon: '🕶️',
    image: 'https://images.unsplash.com/photo-1556306535-38febf6782e7?w=400',
    type: CategoryType.accessories,
    subCategories: [
      SubCategory(
        id: 'cases',
        name: 'علب النظارات',
        nameEn: 'Glasses Cases',
        description: 'علب حماية للنظارات',
        icon: '📦',
      ),
      SubCategory(
        id: 'cleaning_lenses',
        name: 'عدسات تنظيف',
        nameEn: 'Cleaning Lenses',
        description: 'عدسات تنظيف متخصصة',
        icon: '🧽',
      ),
      SubCategory(
        id: 'cleaning_cloths',
        name: 'محارم التنظيف',
        nameEn: 'Cleaning Cloths',
        description: 'محارم خاصة بتنظيف النظارات',
        icon: '🧻',
      ),
      SubCategory(
        id: 'chains',
        name: 'سلاسل النظارات',
        nameEn: 'Glasses Chains',
        description: 'سلاسل وأحبال للنظارات',
        icon: '⛓️',
      ),
      SubCategory(
        id: 'lens_protectors',
        name: 'واقي عدسات',
        nameEn: 'Lens Protectors',
        description: 'واقيات للعدسات',
        icon: '🛡️',
      ),
      SubCategory(
        id: 'bluelight_filters',
        name: 'واقي الأشعة الزرقاء',
        nameEn: 'Blue Light Filters',
        description: 'واقي ضد الأشعة الزرقاء',
        icon: '💙',
      ),
    ],
  ),

  // 3. العدسات
  Category(
    id: 'lenses',
    name: 'العدسات',
    nameEn: 'Lenses',
    description: 'عدسات طبية ولاصقة',
    icon: '🔍',
    image: 'https://images.unsplash.com/photo-1582142306909-195724d33c9f?w=400',
    type: CategoryType.lenses,
    subCategories: [
      SubCategory(
        id: 'prescription_lenses',
        name: 'عدسات طبية',
        nameEn: 'Prescription Lenses',
        description: 'عدسات طبية متنوعة',
        icon: '👁️',
      ),
      SubCategory(
        id: 'clear_lenses',
        name: 'شفافة',
        nameEn: 'Clear Lenses',
        description: 'عدسات شفافة',
        icon: '⚪',
      ),
      SubCategory(
        id: 'anti_reflection',
        name: 'مضادة للانعكاس',
        nameEn: 'Anti-Reflection',
        description: 'عدسات مضادة للانعكاس',
        icon: '✨',
      ),
      SubCategory(
        id: 'photochromic',
        name: 'انتقالية',
        nameEn: 'Photochromic',
        description: 'عدسات تتغير مع الضوء',
        icon: '🌓',
      ),
      SubCategory(
        id: 'contact_lenses',
        name: 'عدسات لاصقة',
        nameEn: 'Contact Lenses',
        description: 'عدسات لاصقة متنوعة',
        icon: '👁️‍🗨️',
      ),
      SubCategory(
        id: 'daily_contacts',
        name: 'يومية',
        nameEn: 'Daily Contacts',
        description: 'عدسات لاصقة يومية',
        icon: '📅',
      ),
      SubCategory(
        id: 'monthly_contacts',
        name: 'شهرية',
        nameEn: 'Monthly Contacts',
        description: 'عدسات لاصقة شهرية',
        icon: '📆',
      ),
      SubCategory(
        id: 'colored_contacts',
        name: 'ملونة',
        nameEn: 'Colored Contacts',
        description: 'عدسات لاصقة ملونة',
        icon: '🌈',
      ),
      SubCategory(
        id: 'clear_contacts',
        name: 'بدون لون',
        nameEn: 'Clear Contacts',
        description: 'عدسات لاصقة شفافة',
        icon: '⚪',
      ),
      SubCategory(
        id: 'contact_solutions',
        name: 'محاليل العدسات',
        nameEn: 'Contact Solutions',
        description: 'محاليل تنظيف وتخزين',
        icon: '🧪',
      ),
    ],
  ),

  // 4. الخدمات
  Category(
    id: 'services',
    name: 'الخدمات',
    nameEn: 'Services',
    description: 'خدمات متخصصة للعيون والنظارات',
    icon: '👨‍⚕️',
    image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400',
    type: CategoryType.services,
    subCategories: [
      SubCategory(
        id: 'eye_test_online',
        name: 'اختبار نظر عبر الإنترنت',
        nameEn: 'Online Eye Test',
        description: 'فحص النظر من المنزل',
        icon: '👁️',
      ),
      SubCategory(
        id: 'appointment_booking',
        name: 'حجز موعد',
        nameEn: 'Appointment Booking',
        description: 'حجز موعد مع أخصائي',
        icon: '📅',
      ),
      SubCategory(
        id: 'glasses_adjustment',
        name: 'تعديل النظارات',
        nameEn: 'Glasses Adjustment',
        description: 'تعديل وتثبيت النظارات',
        icon: '🔧',
      ),
      SubCategory(
        id: 'prescription_service',
        name: 'خدمة الوصفة الطبية',
        nameEn: 'Prescription Service',
        description: 'إدخال الوصفة الطبية',
        icon: '📋',
      ),
    ],
  ),

  // 5. دليل الشراء والمحتوى التوعوي
  Category(
    id: 'content',
    name: 'دليل الشراء',
    nameEn: 'Buying Guide',
    description: 'مقالات ونصائح متخصصة',
    icon: '🧠',
    image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
    type: CategoryType.content,
    subCategories: [
      SubCategory(
        id: 'face_shape_guide',
        name: 'اختيار النظارة المناسبة',
        nameEn: 'Face Shape Guide',
        description: 'كيف تختار النظارة المناسبة لشكل وجهك؟',
        icon: '👤',
      ),
      SubCategory(
        id: 'lens_types_guide',
        name: 'أنواع العدسات',
        nameEn: 'Lens Types Guide',
        description: 'الفرق بين العدسات وأنواعها',
        icon: '🔍',
      ),
      SubCategory(
        id: 'blue_light_importance',
        name: 'أهمية الحماية من الأشعة الزرقاء',
        nameEn: 'Blue Light Protection',
        description: 'فوائد الحماية من الأشعة الزرقاء',
        icon: '💙',
      ),
      SubCategory(
        id: 'fashion_trends_2025',
        name: 'صيحات النظارات 2025',
        nameEn: 'Fashion Trends 2025',
        description: 'أحدث صيحات النظارات',
        icon: '✨',
      ),
    ],
  ),
];
