import 'package:flutter/material.dart';
import 'dart:async';
import '../models/product.dart';
import '../screens/product/product_detail_screen.dart';
import '../utils/mouse_event_handler.dart';
import '../utils/image_helper.dart';
import '../controllers/cart_controller.dart';
import 'package:get/get.dart';

class EnhancedProductCard extends StatefulWidget {
  final Product product;
  final bool isGridView;
  final bool showQuantityControls;

  const EnhancedProductCard({
    super.key,
    required this.product,
    this.isGridView = false,
    this.showQuantityControls = true,
  });

  @override
  State<EnhancedProductCard> createState() => _EnhancedProductCardState();
}

class _EnhancedProductCardState extends State<EnhancedProductCard> {
  CartController? _cartController;
  int quantity = 1;
  Timer? _countdownTimer;
  Duration? _remainingTime;

  @override
  void initState() {
    super.initState();
    _initializeCartController();
    _startCountdown();
  }

  // تهيئة CartController بشكل آمن
  void _initializeCartController() {
    try {
      _cartController = Get.find<CartController>();
    } catch (e) {
      // إذا لم يكن CartController متاحاً، سيتم تهيئته لاحقاً
      _cartController = null;
    }
  }

  // الحصول على CartController بشكل آمن
  CartController? get cartController {
    if (_cartController == null) {
      try {
        _cartController = Get.find<CartController>();
      } catch (e) {
        return null;
      }
    }
    return _cartController;
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    if (widget.product.discountEndDate != null) {
      final now = DateTime.now();
      final endDate = widget.product.discountEndDate!;

      if (endDate.isAfter(now)) {
        _remainingTime = endDate.difference(now);

        _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
          if (mounted) {
            setState(() {
              _remainingTime = endDate.difference(DateTime.now());
              if (_remainingTime!.isNegative) {
                _countdownTimer?.cancel();
                _remainingTime = null;
              }
            });
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeGestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => ProductDetailScreen(product: widget.product),
          ),
        );
      },
      child: SafeInteractionWrapper(
        child: Container(
          height: 280, // ارتفاع ثابت لتجنب overflow
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              )
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // صورة المنتج (بدون شارات)
              _buildCleanProductImage(),

            // معلومات المنتج
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // اسم المنتج
                    Text(
                      widget.product.name,
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // العلامة التجارية
                    Text(
                      widget.product.brand,
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                        height: 1.1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // التقييم
                    _buildRating(),

                    const SizedBox(height: 6),

                    // السعر
                    _buildPriceSection(),

                    const SizedBox(height: 6),

                    // الشارات السفلية (المؤقت، التخفيض، الشحن المجاني)
                    _buildBottomBadges(),

                    const Spacer(),

                    // زر الإضافة للسلة
                    if (widget.showQuantityControls)
                      _buildSimpleActionButtons(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }



  // صورة المنتج النظيفة (بدون شارات)
  Widget _buildCleanProductImage() {
    return Container(
      height: 140,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        color: Colors.grey[100],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        child: widget.product.imageUrls.isNotEmpty
            ? ImageHelper.buildProductImage(
                imageUrl: widget.product.imageUrls.first,
                width: double.infinity,
                height: 140,
                fallbackType: widget.product.type.toString().split('.').last,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
              )
            : ImageHelper.buildColoredPlaceholder(
                width: double.infinity,
                height: 140,
                text: widget.product.name,
                backgroundColor: _getProductColor(widget.product.type),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
              ),
      ),
    );
  }

  // الشارات السفلية (المؤقت، التخفيض، الشحن المجاني)
  Widget _buildBottomBadges() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: [
        // شارة التخفيض
        if (widget.product.originalPrice != null &&
            widget.product.originalPrice! > widget.product.price)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.red.shade600, Colors.red.shade700],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  color: Colors.red.withValues(alpha: 0.3),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Text(
              'خصم ${(((widget.product.originalPrice! - widget.product.price) / widget.product.originalPrice!) * 100).round()}%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

        // مؤقت التخفيض
        if (_remainingTime != null && widget.product.originalPrice != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange.shade600, Colors.orange.shade700],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.3),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.access_time,
                  color: Colors.white,
                  size: 10,
                ),
                const SizedBox(width: 2),
                Text(
                  _formatCountdown(_remainingTime!),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 9,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

        // شارة الشحن المجاني
        if (widget.product.hasFreeShipping)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.green.shade600,
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withValues(alpha: 0.3),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: const Text(
              'شحن مجاني',
              style: TextStyle(
                color: Colors.white,
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

        // شارة المخزون المنخفض
        if (widget.product.stockQuantity <= 5 &&
            widget.product.stockQuantity > 0)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.orange.shade600,
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.3),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Text(
              'متبقي ${widget.product.stockQuantity}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildRating() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(5, (index) {
          return Icon(
            index < widget.product.rating.floor()
                ? Icons.star
                : index < widget.product.rating
                    ? Icons.star_half
                    : Icons.star_border,
            size: 10,
            color: Colors.amber,
          );
        }),
        const SizedBox(width: 2),
        Text(
          '(${widget.product.reviewCount})',
          style: TextStyle(
            fontSize: 9,
            color: Colors.grey[600],
            height: 1.0,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // السعر الحالي
        Text(
          '${widget.product.price.toStringAsFixed(0)} دج',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.green,
            height: 1.1,
          ),
        ),

        // السعر الأصلي (إذا كان هناك تخفيض)
        if (widget.product.originalPrice != null &&
            widget.product.originalPrice! > widget.product.price)
          Text(
            '${widget.product.originalPrice!.toStringAsFixed(0)} دج',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
              decoration: TextDecoration.lineThrough,
              height: 1.0,
            ),
          ),
      ],
    );
  }

  // أيقونة سلة صغيرة
  Widget _buildSimpleActionButtons() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        width: 28,
        height: 28,
        decoration: BoxDecoration(
          color: widget.product.stockQuantity > 0 ? Colors.blue : Colors.grey,
          borderRadius: BorderRadius.circular(14),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(14),
            onTap: widget.product.stockQuantity > 0
                ? () {
                    final controller = cartController;
                    if (controller != null) {
                      controller.addToCart(widget.product);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('تم إضافة ${widget.product.name} للسلة'),
                          duration: const Duration(seconds: 2),
                          backgroundColor: Colors.green,
                        ),
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى تسجيل الدخول أولاً'),
                          duration: Duration(seconds: 2),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    }
                  }
                : null,
            child: Icon(
              widget.product.stockQuantity > 0
                  ? Icons.shopping_cart_outlined
                  : Icons.remove_shopping_cart,
              color: Colors.white,
              size: 14,
            ),
          ),
        ),
      ),
    );
  }

  // تنسيق مؤقت التخفيض
  String _formatCountdown(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}د ${duration.inHours % 24}س';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}س ${duration.inMinutes % 60}د';
    } else {
      return '${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
    }
  }

  // تحديد لون المنتج حسب النوع
  Color _getProductColor(EyewearType type) {
    switch (type) {
      case EyewearType.sunglasses:
        return const Color(0xFF4A90E2);
      case EyewearType.reading:
        return const Color(0xFF50C878);
      case EyewearType.fashion:
        return const Color(0xFFFF6B6B);
      case EyewearType.sports:
        return const Color(0xFFFFA500);
      case EyewearType.prescription:
        return const Color(0xFF9370DB);
    }
  }
}
